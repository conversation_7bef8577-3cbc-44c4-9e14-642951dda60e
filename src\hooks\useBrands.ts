import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '../utils/supabase/client';

export interface Brand {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  country?: string;
  founded_year?: number;
  is_active: boolean;
  product_count: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface BrandFilters {
  include_inactive?: boolean;
  search?: string;
  country?: string;
}

export interface CreateBrandData {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  country?: string;
  founded_year?: number;
  is_active?: boolean;
  metadata?: Record<string, any>;
}

export interface UpdateBrandData extends Partial<CreateBrandData> {
  id: string;
}

// Hook to fetch all brands with optional filters
export const useBrands = (filters?: BrandFilters) => {
  const queryParams = new URLSearchParams();
  
  if (filters?.include_inactive) {
    queryParams.set('include_inactive', 'true');
  }
  if (filters?.search) {
    queryParams.set('search', filters.search);
  }
  if (filters?.country) {
    queryParams.set('country', filters.country);
  }

  return useQuery({
    queryKey: ['brands', filters],
    queryFn: async (): Promise<Brand[]> => {
      const url = `/api/brands${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch brands');
      }
      
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to fetch a single brand by ID
export const useBrand = (id: string) => {
  return useQuery({
    queryKey: ['brands', id],
    queryFn: async (): Promise<Brand> => {
      const response = await fetch(`/api/brands/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch brand');
      }
      
      return response.json();
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Hook to create a new brand (admin only)
export const useCreateBrand = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (brandData: CreateBrandData): Promise<Brand> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/brands', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(brandData),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create brand');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch brands
      queryClient.invalidateQueries({ queryKey: ['brands'] });
    },
  });
};

// Hook to update a brand (admin only)
export const useUpdateBrand = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateBrandData): Promise<Brand> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/brands/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(updateData),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update brand');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      // Invalidate and refetch brands
      queryClient.invalidateQueries({ queryKey: ['brands'] });
      // Update the specific brand in cache
      queryClient.setQueryData(['brands', data.id], data);
    },
  });
};

// Hook to delete a brand (admin only)
export const useDeleteBrand = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/brands/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete brand');
      }
    },
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch brands
      queryClient.invalidateQueries({ queryKey: ['brands'] });
      // Remove the specific brand from cache
      queryClient.removeQueries({ queryKey: ['brands', deletedId] });
    },
  });
};

// Hook to get countries list from brands
export const useBrandCountries = () => {
  return useQuery({
    queryKey: ['brand-countries'],
    queryFn: async (): Promise<string[]> => {
      const response = await fetch('/api/brands?include_inactive=true');
      
      if (!response.ok) {
        throw new Error('Failed to fetch brands');
      }
      
      const brands: Brand[] = await response.json();
      const uniqueCountries = new Set(brands.map(brand => brand.country).filter(Boolean));
      const countries = Array.from(uniqueCountries);
      return countries.sort();
    },
    staleTime: 30 * 60 * 1000, // 30 minutes (countries don't change often)
  });
};
