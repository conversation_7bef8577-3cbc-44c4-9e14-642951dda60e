"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Settings,
  BarChart3,
  LogOut,
  ChevronLeft,
  ChevronRight,
  GraduationCap,
  FileText,
  Activity,
  Calendar,
  PenTool,
  FolderOpen,
  MessageSquare,
  TrendingUp,
  Tag,
  Award,
  ExternalLink
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { createClient } from '@/utils/supabase/client';
import { SystemActivityLogger } from '@/utils/admin-activity-logger';
import { useRouter } from "next/navigation";

const getMenuItems = (adminRole: string | null) => {
  const baseItems = [
    {
      title: "Dashboard",
      href: "/admin",
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      title: "Orders",
      href: "/admin/orders",
      icon: <ShoppingCart className="h-5 w-5" />,
    },
    {
      title: "Products",
      href: "/admin/products",
      icon: <Package className="h-5 w-5" />,
    },
    {
      title: "Categories",
      href: "/admin/categories",
      icon: <Tag className="h-5 w-5" />,
    },
    {
      title: "Brands",
      href: "/admin/brands",
      icon: <Award className="h-5 w-5" />,
    },
    {
      title: "Users",
      href: "/admin/users",
      icon: <Users className="h-5 w-5" />,
    },
    {
      title: "Mentorship",
      href: "/admin/mentorship",
      icon: <GraduationCap className="h-5 w-5" />,
    },
    {
      title: "Consultations",
      href: "/admin/consultations",
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      title: "Analytics",
      href: "/admin/analytics",
      icon: <BarChart3 className="h-5 w-5" />,
    },
  ];

  // Add blog management section for all admin types
  const blogItems = [
    {
      title: "Blog Posts",
      href: "/admin/blog/posts",
      icon: <PenTool className="h-5 w-5" />,
    },
    {
      title: "Blog Categories",
      href: "/admin/blog/categories",
      icon: <FolderOpen className="h-5 w-5" />,
    },
    {
      title: "Blog Comments",
      href: "/admin/blog/comments",
      icon: <MessageSquare className="h-5 w-5" />,
    },
    {
      title: "Blog Analytics",
      href: "/admin/blog/analytics",
      icon: <TrendingUp className="h-5 w-5" />,
    },
  ];

  // Add blog items to base items
  baseItems.push(...blogItems);

  // Add monitoring and reports for main admins only
  if (adminRole === 'admin') {
    baseItems.push(
      {
        title: "Activity Monitor",
        href: "/admin/activity",
        icon: <Activity className="h-5 w-5" />,
      },
      {
        title: "Reports",
        href: "/admin/reports",
        icon: <FileText className="h-5 w-5" />,
      }
    );
  }

  return baseItems;
};

export function AdminSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [adminRole, setAdminRole] = useState<string | null>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const supabase = createClient();

  // Handle logout function
  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      router.push('/admin/sign-in');
    } catch (error) {
      console.error('Error signing out:', error);
      router.push('/admin/sign-in');
    }
  };

  // Log page access when pathname changes
  useEffect(() => {
    if (currentUserId && pathname) {
      const pageName = pathname.split('/').pop() || 'dashboard';
      SystemActivityLogger.settingsChanged(
        'page_access',
        '',
        `Accessed ${pageName} page`
      );
    }
  }, [pathname, currentUserId]);

  useEffect(() => {
    const fetchAdminRole = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) return;

        const { data: userData } = await supabase
          .from('users')
          .select('admin_role')
          .eq('id', session.user.id)
          .single();

        if (userData) {
          setAdminRole(userData.admin_role);
          setCurrentUserId(session.user.id);
        }
      } catch (error) {
        console.error('Error fetching admin role:', error);
      }
    };

    fetchAdminRole();
  }, []);

  const menuItems = getMenuItems(adminRole);

  return (
    <div className={cn(
      "glass-effect-dark border-r border-white/10 h-screen transition-all duration-300 relative neo-shadow flex flex-col",
      collapsed ? "w-[80px] rounded-r-[40px]" : "w-[280px]"
    )}>
      {/* Header with Logo */}
      <div className={cn(
        "flex items-center justify-between border-b border-white/10 flex-shrink-0",
        collapsed ? "p-2" : "p-4"
      )}>
        <Link href="/admin" className={cn(
          "font-bold text-xl transition-opacity text-foreground",
          collapsed ? "opacity-0 invisible" : "opacity-100 visible"
        )}>
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full neo-shadow-light bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
              <span className="text-primary font-bold text-sm">TW</span>
            </div>
            <span className="gradient-text">Admin</span>
          </div>
        </Link>

        {/* Collapse Toggle Button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setCollapsed(!collapsed)}
          className={cn(
            "glass-effect-subtle border border-white/20 rounded-full z-10 neo-shadow hover:neo-shadow-light transition-neo h-8 w-8",
            collapsed ? "mx-auto" : "absolute right-[-12px] top-6"
          )}
        >
          {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      {/* Scrollable Navigation Area */}
      <ScrollArea className="flex-1 py-4">
        <div className={cn("space-y-2", collapsed ? "px-2" : "px-3")}>
          {/* Main Navigation Items */}
          {menuItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center rounded-2xl text-sm font-medium transition-all duration-300 group min-h-[44px] relative overflow-hidden",
                pathname === item.href
                  ? "glass-effect text-primary neo-shadow-inset border border-primary/20"
                  : "text-muted-foreground hover:glass-effect-subtle hover:text-foreground hover:neo-shadow-light border border-transparent hover:border-white/10",
                collapsed ? "justify-center px-2 py-3" : "justify-start px-4 py-3"
              )}
            >
              {/* Hover background effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />

              <div className={cn(
                "flex items-center justify-center w-5 h-5 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3 relative z-10",
                pathname === item.href ? "text-primary" : ""
              )}>
                {item.icon}
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 relative z-10",
                collapsed ? "opacity-0 invisible w-0" : "opacity-100 visible w-auto"
              )}>
                {item.title}
              </span>
              {pathname === item.href && !collapsed && (
                <div className="ml-auto w-2 h-2 rounded-full bg-primary animate-pulse relative z-10"></div>
              )}

              {/* Active indicator line */}
              {pathname === item.href && (
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-primary rounded-r-full"></div>
              )}
            </Link>
          ))}

          {/* Separator */}
          <Separator className={cn(
            "my-4 bg-white/10",
            collapsed ? "mx-2" : "mx-4"
          )} />

          {/* Settings */}
          <Link
            href="/admin/settings"
            className={cn(
              "flex items-center rounded-2xl text-sm font-medium transition-all duration-300 group min-h-[44px] relative overflow-hidden",
              pathname === "/admin/settings"
                ? "glass-effect text-primary neo-shadow-inset border border-primary/20"
                : "text-muted-foreground hover:glass-effect-subtle hover:text-foreground hover:neo-shadow-light border border-transparent hover:border-white/10",
              collapsed ? "justify-center px-2 py-3" : "justify-start px-4 py-3"
            )}
          >
            {/* Hover background effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />

            <div className={cn(
              "flex items-center justify-center w-5 h-5 transition-all duration-300 group-hover:scale-110 group-hover:rotate-12 relative z-10",
              pathname === "/admin/settings" ? "text-primary" : ""
            )}>
              <Settings className="h-5 w-5" />
            </div>
            <span className={cn(
              "ml-3 transition-all duration-300 relative z-10",
              collapsed ? "opacity-0 invisible w-0" : "opacity-100 visible w-auto"
            )}>
              Settings
            </span>
            {pathname === "/admin/settings" && !collapsed && (
              <div className="ml-auto w-2 h-2 rounded-full bg-primary animate-pulse relative z-10"></div>
            )}

            {/* Active indicator line */}
            {pathname === "/admin/settings" && (
              <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-primary rounded-r-full"></div>
            )}
          </Link>
        </div>
      </ScrollArea>

      {/* Bottom Actions - Fixed at bottom */}
      <div className={cn(
        "flex-shrink-0 border-t border-white/10 space-y-2",
        collapsed ? "p-2" : "p-3"
      )}>
        {/* Exit to Store Button */}
        <Button
          variant="ghost"
          className={cn(
            "w-full flex items-center text-muted-foreground hover:glass-effect-subtle hover:text-foreground rounded-2xl py-3 min-h-[44px] font-medium transition-all duration-300 border border-transparent hover:border-white/20 hover:neo-shadow-light group relative overflow-hidden",
            collapsed ? "justify-center px-2" : "justify-start px-4"
          )}
          asChild
        >
          <Link href="/">
            {/* Hover background effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-green-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />

            <ExternalLink className="h-5 w-5 transition-all duration-300 group-hover:scale-110 group-hover:-rotate-12 relative z-10" />
            <span className={cn(
              "ml-3 transition-all duration-300 relative z-10",
              collapsed ? "opacity-0 invisible w-0" : "opacity-100 visible w-auto"
            )}>
              Exit to Store
            </span>
          </Link>
        </Button>

        {/* Logout Button */}
        <Button
          variant="ghost"
          onClick={handleLogout}
          className={cn(
            "w-full flex items-center text-destructive hover:glass-effect-subtle hover:text-destructive rounded-2xl py-3 min-h-[44px] font-medium transition-all duration-300 border border-transparent hover:border-destructive/20 hover:neo-shadow-light group relative overflow-hidden",
            collapsed ? "justify-center px-2" : "justify-start px-4"
          )}
        >
          {/* Hover background effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-red-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />

          <LogOut className="h-5 w-5 transition-all duration-300 group-hover:scale-110 group-hover:rotate-12 relative z-10" />
          <span className={cn(
            "ml-3 transition-all duration-300 relative z-10",
            collapsed ? "opacity-0 invisible w-0" : "opacity-100 visible w-auto"
          )}>
            Logout
          </span>
        </Button>
      </div>
    </div>
  );
}
